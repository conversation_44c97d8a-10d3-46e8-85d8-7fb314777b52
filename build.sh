#!/bin/bash

# Netlify 構建腳本
echo "🚀 開始構建 TVBOX..."

# 顯示環境信息
echo "📊 環境信息:"
echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo "當前目錄: $(pwd)"
echo "環境變量: NODE_ENV=$NODE_ENV, NETLIFY=$NETLIFY"

# 檢查 package.json
if [ ! -f "package.json" ]; then
    echo "❌ 找不到 package.json 文件"
    exit 1
fi

echo "✅ 找到 package.json"

# 檢查 package-lock.json
if [ ! -f "package-lock.json" ]; then
    echo "⚠️ 找不到 package-lock.json，使用 npm install"
    npm install
else
    echo "✅ 找到 package-lock.json，使用 npm ci"
    npm ci
fi

# 檢查 node_modules
if [ ! -d "node_modules" ]; then
    echo "❌ node_modules 目錄不存在"
    exit 1
fi

echo "✅ 依賴安裝完成"

# 檢查 vite 是否存在
if [ ! -f "node_modules/.bin/vite" ]; then
    echo "❌ 找不到 vite，嘗試重新安裝..."
    npm install vite --save-dev
fi

echo "✅ vite 可用"

# 列出可用的 npm 腳本
echo "📋 可用的 npm 腳本:"
npm run

# 執行構建
echo "🔨 開始構建..."
npm run build

# 檢查構建結果
if [ ! -d "dist" ]; then
    echo "❌ 構建失敗：找不到 dist 目錄"
    exit 1
fi

echo "✅ 構建成功！"
echo "📁 dist 目錄內容:"
ls -la dist/

echo "🎉 TVBOX 構建完成！"
