import React, { useEffect, useRef, useCallback } from 'react';
import Hls from 'hls.js';
import { usePlayerStore } from '../../stores/playerStore';
import type { M3UChannel } from '../../types';

interface HLSPlayerProps {
  channel: M3UChannel;
  autoplay?: boolean;
  className?: string;
}

export const HLSPlayer: React.FC<HLSPlayerProps> = ({
  channel,
  autoplay = false,
  className = ''
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const retryCountRef = useRef(0);
  const maxRetries = 3;

  const {
    isPlaying,
    volume,
    isMuted,
    setLoading,
    setError,
    clearError,
    setHlsInstance,
    destroyHlsInstance,
    setDuration,
    setCurrentTime,
    play,
    pause
  } = usePlayerStore();

  // 重試播放的函數
  const retryPlayback = useCallback(() => {
    if (retryCountRef.current < maxRetries) {
      retryCountRef.current++;
      console.log(`重試播放，第 ${retryCountRef.current} 次`);

      setTimeout(() => {
        const video = videoRef.current;
        if (video && channel.url) {
          // 重新初始化播放器
          initializePlayer();
        }
      }, 1000 * retryCountRef.current); // 遞增延遲
    } else {
      setError('播放失敗：已達到最大重試次數');
      setLoading(false);
    }
  }, [channel.url]);

  // 初始化播放器的函數
  const initializePlayer = useCallback(() => {
    const video = videoRef.current;
    if (!video || !channel.url) return;

    setLoading(true);
    clearError();

    console.log('🎬 初始化播放器');
    console.log('📺 頻道:', channel.name);
    console.log('🔗 URL:', channel.url);
    console.log('👤 User-Agent:', channel.userAgent || '預設');
    console.log('🔗 Referer:', channel.referer || '無');

    // 檢查是否支援 HLS
    if (Hls.isSupported()) {
      const hls = new Hls({
        // 基本設定
        enableWorker: true,
        lowLatencyMode: false, // 關閉低延遲模式以提高穩定性

        // 緩衝設定 - 優化穩定性，減少卡頓
        backBufferLength: 30, // 增加後緩衝，提供更好的穩定性
        maxBufferLength: 30, // 增加緩衝長度，減少頻繁載入
        maxMaxBufferLength: 60, // 更大的最大緩衝，應對網路波動
        maxBufferSize: 60 * 1000 * 1000, // 60MB 緩衝大小，提供更多緩衝空間
        maxBufferHole: 0.5, // 放寬緩衝洞容忍度，減少不必要的重新載入

        // 直播同步設定 - 平衡延遲與穩定性
        liveSyncDurationCount: 3, // 適中的同步片段數量
        liveMaxLatencyDurationCount: 10, // 增加最大延遲容忍度
        liveDurationInfinity: false,

        // 片段載入設定 - 增加容錯性
        manifestLoadingTimeOut: 15000, // 增加 manifest 載入超時
        manifestLoadingMaxRetry: 5, // 增加重試次數
        manifestLoadingRetryDelay: 1000, // 適中的重試延遲

        fragLoadingTimeOut: 20000, // 增加片段載入超時
        fragLoadingMaxRetry: 6, // 增加重試次數
        fragLoadingRetryDelay: 500, // 適中的重試延遲

        // 性能優化
        startFragPrefetch: true, // 啟用預載入
        testBandwidth: true, // 啟用頻寬測試
        enableSoftwareAES: true, // 啟用軟體 AES

        // 播放優化 - 增加容錯性
        nudgeOffset: 0.5, // 增加微調偏移，提供更多緩衝空間
        nudgeMaxRetry: 8, // 增加微調重試次數
        maxFragLookUpTolerance: 0.5, // 增加片段查找容忍度
        maxSeekHole: 5, // 增加最大尋找洞

        // 錯誤恢復 - 減少監控頻率，提高穩定性
        highBufferWatchdogPeriod: 5, // 增加高緩衝監控週期
        lowBufferWatchdogPeriod: 2, // 增加低緩衝監控週期

        // 新增錯誤恢復超時設定
        fragLoadingMaxRetryTimeout: 120000, // 2分鐘最大重試超時
        levelLoadingMaxRetryTimeout: 120000, // 層級載入最大重試超時
        manifestLoadingMaxRetryTimeout: 120000, // manifest 最大重試超時

        // ABR (自適應位元率) 設定 - 優化穩定性，減少卡頓
        abrEwmaFastLive: 5.0, // 更保守的快速頻寬估算，減少頻繁切換
        abrEwmaSlowLive: 15.0, // 更慢的頻寬估算，提供更穩定的品質選擇
        abrEwmaDefaultEstimate: 1000000, // 提高預設頻寬估算到 1Mbps
        abrBandWidthFactor: 0.8, // 更保守的頻寬因子
        abrBandWidthUpFactor: 0.6, // 更保守的上調因子
        abrMaxWithRealBitrate: false,
        maxStarvationDelay: 8, // 增加飢餓延遲容忍度
        maxLoadingDelay: 8, // 增加載入延遲容忍度

        // 新增更多 ABR 控制 - 穩定性優先
        startLevel: -1, // 讓 HLS.js 自動選擇起始品質
        capLevelToPlayerSize: false, // 不限制品質到播放器大小，避免不必要的降級
        capLevelOnFPSDrop: false, // 關閉 FPS 降級，減少品質波動
        // 新增 XHR 設定來處理 CORS 和自定義標頭
        xhrSetup: (xhr: XMLHttpRequest, url: string) => {
          console.log('🌐 設定 XHR 請求:', url);

          // 如果頻道有自定義的 User-Agent
          if (channel.userAgent) {
            console.log('👤 設定 User-Agent:', channel.userAgent);
            try {
              xhr.setRequestHeader('User-Agent', channel.userAgent);
            } catch (e) {
              console.warn('⚠️ 無法設定 User-Agent (瀏覽器限制):', e);
            }
          }

          // 如果頻道有自定義的 Referer
          if (channel.referer) {
            console.log('🔗 設定 Referer:', channel.referer);
            try {
              xhr.setRequestHeader('Referer', channel.referer);
            } catch (e) {
              console.warn('⚠️ 無法設定 Referer (瀏覽器限制):', e);
            }
          }

          // 設定超時時間
          xhr.timeout = 30000; // 30 秒
        }
      });

      // 設定 HLS 實例到 store
      setHlsInstance(hls);

      // 載入影片源
      hls.loadSource(channel.url);
      hls.attachMedia(video);

      // HLS 事件處理
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS manifest 解析完成');
        setLoading(false);
        retryCountRef.current = 0; // 重置重試計數

        if (autoplay) {
          video.play().catch(error => {
            console.error('Autoplay failed:', error);
            setError('自動播放失敗，請手動點擊播放');
          });
        }
      });

      hls.on(Hls.Events.MEDIA_ATTACHED, () => {
        console.log('HLS 媒體已附加');
      });

      hls.on(Hls.Events.FRAG_LOADED, () => {
        // 片段載入成功，確保載入狀態已清除
        setLoading(false);
      });

      // 新增更多事件監聽以優化播放體驗
      hls.on(Hls.Events.BUFFER_APPENDED, () => {
        console.log('📦 緩衝區已更新');
        setLoading(false);
      });

      hls.on(Hls.Events.BUFFER_EOS, () => {
        console.log('📦 緩衝區結束');
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (_, data) => {
        const level = hls.levels[data.level];
        console.log(`🔄 品質切換到: Level ${data.level} (${level?.height || '未知'}p, ${Math.round((level?.bitrate || 0) / 1000)}kbps)`);
      });

      hls.on(Hls.Events.FRAG_BUFFERED, () => {
        console.log('📦 片段已緩衝');
      });

      // 添加更詳細的診斷事件和自動恢復機制
      hls.on(Hls.Events.BUFFER_STALLED, () => {
        console.warn('⚠️ 緩衝停滯，嘗試恢復...');
        // 嘗試恢復播放
        const video = videoRef.current;
        if (video && video.paused && !video.ended) {
          video.play().catch(console.error);
        }

        // 如果緩衝停滯，嘗試降低品質
        if (hls.currentLevel > 0) {
          console.log('🔽 降低品質以改善播放穩定性');
          hls.currentLevel = Math.max(0, hls.currentLevel - 1);
        }
      });

      hls.on(Hls.Events.BUFFER_NUDGED, () => {
        console.log('🔧 緩衝區已微調');
      });

      hls.on(Hls.Events.FRAG_LOADING, (_, data) => {
        console.log(`⬇️ 載入片段: ${data.frag.sn} (${data.frag.url})`);
      });

      hls.on(Hls.Events.FRAG_LOAD_PROGRESS, (_, data) => {
        const progress = Math.round((data.stats.loaded / data.stats.total) * 100);
        if (progress % 25 === 0) { // 只在 25%, 50%, 75%, 100% 時記錄
          console.log(`📊 片段載入進度: ${progress}%`);
        }
      });

      hls.on(Hls.Events.ERROR, (_, data) => {
        console.error('❌ HLS 錯誤:', data);

        // 詳細錯誤資訊
        const errorDetails = {
          type: data.type,
          details: data.details,
          fatal: data.fatal,
          url: data.url || channel.url,
          response: data.response,
          reason: data.reason
        };

        console.error('🔍 錯誤詳情:', errorDetails);

        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.log('🌐 網路錯誤，嘗試重試...');

              // 檢查具體的網路錯誤類型
              if (data.details === Hls.ErrorDetails.MANIFEST_LOAD_ERROR) {
                setError(`無法載入播放清單：${data.response?.code || '未知錯誤'}\n可能是 CORS 限制或伺服器無法存取`);
              } else if (data.details === Hls.ErrorDetails.FRAG_LOAD_ERROR) {
                setError('無法載入影片片段，可能是網路問題');
              } else {
                setError(`網路錯誤：${data.details || '未知網路問題'}`);
              }

              retryPlayback();
              break;

            case Hls.ErrorTypes.MEDIA_ERROR:
              console.log('🎵 媒體錯誤，嘗試恢復...');
              try {
                hls.recoverMediaError();
                console.log('✅ 媒體錯誤已恢復');
              } catch (err) {
                console.error('❌ 無法恢復媒體錯誤:', err);
                setError('媒體解碼錯誤，無法播放此格式');
                retryPlayback();
              }
              break;

            case Hls.ErrorTypes.MUX_ERROR:
              console.log('📦 多工錯誤');
              setError('影片格式錯誤，無法解析');
              setLoading(false);
              break;

            default:
              console.log('❓ 其他致命錯誤');
              setError(`播放錯誤：${data.details || '無法播放此頻道'}`);
              setLoading(false);
              break;
          }
        } else {
          // 非致命錯誤，記錄但不中斷播放
          console.warn('⚠️ HLS 非致命錯誤:', data);

          // 某些非致命錯誤也可能需要處理
          if (data.details === Hls.ErrorDetails.BUFFER_STALLED_ERROR) {
            console.log('🔄 緩衝停滯，嘗試恢復...');
          }
        }
      });

      // 清理函數
      return () => {
        hls.destroy();
      };
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Safari 原生支援 HLS
      console.log('🍎 使用 Safari 原生 HLS 支援');
      video.src = channel.url;
      setLoading(false);
      retryCountRef.current = 0; // 重置重試計數

      if (autoplay) {
        video.play().catch(error => {
          console.error('Autoplay failed:', error);
          setError('自動播放失敗，請手動點擊播放');
        });
      }
    } else {
      // 嘗試直接播放（可能是 MP4 或其他格式）
      console.log('🎬 嘗試直接播放非 HLS 格式');
      console.log('📺 URL:', channel.url);

      // 檢查是否為支援的影片格式
      const supportedFormats = [
        'video/mp4',
        'video/webm',
        'video/ogg',
        'application/x-mpegURL',
        'application/vnd.apple.mpegurl'
      ];

      let canPlay = false;
      for (const format of supportedFormats) {
        if (video.canPlayType(format)) {
          console.log(`✅ 支援格式: ${format}`);
          canPlay = true;
          break;
        }
      }

      if (canPlay) {
        console.log('🎯 嘗試直接設定 video src');
        video.src = channel.url;
        setLoading(false);
        retryCountRef.current = 0;

        if (autoplay) {
          video.play().catch(error => {
            console.error('❌ 直接播放失敗:', error);
            setError('自動播放失敗，請手動點擊播放');
          });
        }
      } else {
        console.error('❌ 瀏覽器不支援此格式');
        setError('您的瀏覽器不支援此影片格式，請嘗試使用其他瀏覽器或播放器');
        setLoading(false);
      }
    }
  }, [channel.url, autoplay, setLoading, clearError, setHlsInstance, setError, retryPlayback]);

  useEffect(() => {
    // 重置重試計數
    retryCountRef.current = 0;

    // 初始化播放器
    initializePlayer();

    // 清理函數
    return () => {
      destroyHlsInstance();
    };
  }, [channel.url, initializePlayer, destroyHlsInstance]);

  // 處理播放/暫停狀態
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.play().catch(error => {
        console.error('Play failed:', error);
        setError('播放失敗');
      });
    } else {
      video.pause();
    }
  }, [isPlaying, setError]);

  // 處理音量變化
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    video.volume = isMuted ? 0 : volume;
    video.muted = isMuted;
  }, [volume, isMuted]);

  // 影片事件處理
  const handleLoadedMetadata = () => {
    const video = videoRef.current;
    if (video) {
      setDuration(video.duration);
      console.log('影片元數據已載入，時長:', video.duration);
    }
  };

  const handleTimeUpdate = () => {
    const video = videoRef.current;
    if (video) {
      setCurrentTime(video.currentTime);
    }
  };

  const handlePlay = () => {
    console.log('影片開始播放');
    play();
  };

  const handlePause = () => {
    console.log('影片暫停');
    pause();
  };

  const handleError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    const video = e.currentTarget;
    const error = video.error;

    console.error('影片錯誤:', error);

    if (error) {
      switch (error.code) {
        case MediaError.MEDIA_ERR_ABORTED:
          setError('播放被中止');
          break;
        case MediaError.MEDIA_ERR_NETWORK:
          setError('網路錯誤：無法載入影片');
          retryPlayback();
          return; // 不設置 loading 為 false，讓重試機制處理
        case MediaError.MEDIA_ERR_DECODE:
          setError('解碼錯誤：影片格式不支援');
          break;
        case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
          setError('不支援的影片格式');
          break;
        default:
          setError('影片載入失敗');
          break;
      }
    } else {
      setError('影片載入失敗');
    }

    setLoading(false);
  };

  const handleWaiting = () => {
    console.log('影片緩衝中...');
    setLoading(true);
  };

  const handleCanPlay = () => {
    console.log('影片可以播放');
    setLoading(false);
  };

  const handleCanPlayThrough = () => {
    console.log('影片可以流暢播放');
    setLoading(false);
  };

  const handleLoadStart = () => {
    console.log('開始載入影片');
    setLoading(true);
  };

  const handleProgress = () => {
    const video = videoRef.current;
    if (video && video.buffered.length > 0) {
      const currentTime = video.currentTime;
      const bufferedEnd = video.buffered.end(video.buffered.length - 1);
      const bufferedAhead = bufferedEnd - currentTime;

      // 詳細的緩衝資訊（減少日誌頻率）
      if (Math.floor(currentTime) % 5 === 0) { // 每5秒記錄一次
        console.log(`📊 緩衝狀態: 當前 ${currentTime.toFixed(1)}s, 緩衝到 ${bufferedEnd.toFixed(1)}s, 提前 ${bufferedAhead.toFixed(1)}s`);
      }

      // 緩衝健康監控和自動調整
      if (bufferedAhead < 5) {
        console.warn(`⚠️ 緩衝不足: 只有 ${bufferedAhead.toFixed(1)} 秒`);

        // 如果緩衝嚴重不足且有 HLS 實例，嘗試降低品質
        const { hlsInstance } = usePlayerStore.getState();
        if (bufferedAhead < 2 && hlsInstance && hlsInstance.currentLevel > 0) {
          console.log('🔽 緩衝嚴重不足，自動降低品質');
          hlsInstance.currentLevel = Math.max(0, hlsInstance.currentLevel - 1);
        }
      } else if (bufferedAhead > 15) {
        // 緩衝充足，可以考慮提升品質
        const { hlsInstance } = usePlayerStore.getState();
        if (hlsInstance && hlsInstance.currentLevel < hlsInstance.levels.length - 1) {
          console.log('🔼 緩衝充足，可以提升品質');
          // 讓 ABR 自然處理品質提升，不強制設定
        }
      }

      // 檢查是否有緩衝間隙（減少日誌頻率）
      if (video.buffered.length > 1 && Math.floor(currentTime) % 10 === 0) {
        for (let i = 0; i < video.buffered.length; i++) {
          const start = video.buffered.start(i);
          const end = video.buffered.end(i);
          console.log(`📦 緩衝區間 ${i}: ${start.toFixed(1)}s - ${end.toFixed(1)}s`);
        }
      }
    }
  };

  return (
    <video
      ref={videoRef}
      className={`w-full h-full bg-black ${className}`}
      controls
      playsInline
      preload="auto" // 改為 auto 以提前載入更多內容
      crossOrigin="anonymous"
      onLoadStart={handleLoadStart}
      onLoadedMetadata={handleLoadedMetadata}
      onTimeUpdate={handleTimeUpdate}
      onPlay={handlePlay}
      onPause={handlePause}
      onError={handleError}
      onWaiting={handleWaiting}
      onCanPlay={handleCanPlay}
      onCanPlayThrough={handleCanPlayThrough}
      onProgress={handleProgress}
      poster={channel.logo}
      // 增加更多相容性屬性
      autoPlay={false} // 避免自動播放問題
      muted={false}
      webkit-playsinline="true" // iOS Safari 相容性
      // 新增優化屬性
      x5-video-player-type="h5" // 騰訊 X5 內核優化
      x5-video-player-fullscreen="true" // X5 全螢幕支援
      x5-video-orientation="portraint" // X5 方向設定
      style={{
        objectFit: 'contain', // 確保影片比例正確
        backgroundColor: '#000' // 確保背景為黑色
      }}
    >
      <source src={channel.url} type="application/x-mpegURL" />
      <source src={channel.url} type="video/mp4" />
      您的瀏覽器不支援影片播放。
    </video>
  );
};
