import type { M3UChannel, M3UPlaylist, ApiResponse } from '../types';

export class M3UParser {
  /**
   * 從 URL 獲取並解析 M3U 播放清單
   */
  static async parseFromUrl(url: string): Promise<ApiResponse<M3UPlaylist>> {
    try {
      // 驗證 URL 格式
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: '無效的 URL 格式'
        };
      }

      // 獲取 M3U 內容
      const response = await fetch(url);
      if (!response.ok) {
        return {
          success: false,
          error: `無法獲取播放清單: ${response.status} ${response.statusText}`
        };
      }

      const content = await response.text();
      const playlist = this.parseM3UContent(content, url);

      return {
        success: true,
        data: playlist
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '解析播放清單時發生錯誤'
      };
    }
  }

  /**
   * 解析 M3U 內容
   */
  static parseM3UContent(content: string, sourceUrl: string): M3UPlaylist {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    const channels: M3UChannel[] = [];

    // 解析播放清單標頭屬性
    const playlistAttributes = this.parsePlaylistHeader(lines);

    let currentChannel: Partial<M3UChannel> = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.startsWith('#EXTM3U')) {
        // M3U 檔案標頭，已在 parsePlaylistHeader 中處理
        continue;
      }

      if (line.startsWith('#EXTINF:')) {
        // 解析頻道資訊
        currentChannel = this.parseExtInf(line);

        // 繼承播放清單的全域屬性
        if (playlistAttributes.catchup && !currentChannel.catchup) {
          currentChannel.catchup = playlistAttributes.catchup;
        }
        if (playlistAttributes.catchupSource && !currentChannel.catchupSource) {
          currentChannel.catchupSource = playlistAttributes.catchupSource;
        }
        if (playlistAttributes.userAgent && !currentChannel.userAgent) {
          currentChannel.userAgent = playlistAttributes.userAgent;
        }
      } else if (line.startsWith('http://') || line.startsWith('https://') || line.startsWith('rtmp://') || line.startsWith('rtsp://')) {
        // 頻道 URL（支援更多協議）
        if (currentChannel.name) {
          const channel: M3UChannel = {
            id: this.generateChannelId(currentChannel.name, line),
            name: currentChannel.name,
            url: line,
            logo: currentChannel.logo,
            group: currentChannel.group,
            tvgId: currentChannel.tvgId,
            tvgName: currentChannel.tvgName,
            resolution: currentChannel.resolution,
            language: currentChannel.language,
            catchup: currentChannel.catchup,
            catchupSource: currentChannel.catchupSource,
            duration: currentChannel.duration,
            userAgent: currentChannel.userAgent,
            referer: currentChannel.referer
          };
          channels.push(channel);
        }
        currentChannel = {};
      } else if (line.startsWith('#EXT-X-') || line.startsWith('#')) {
        // 其他 M3U 標籤，暫時跳過但不重置 currentChannel
        continue;
      }
    }

    return {
      id: this.generatePlaylistId(sourceUrl),
      name: this.extractPlaylistName(sourceUrl),
      url: sourceUrl,
      channels,
      lastUpdated: new Date(),
      totalChannels: channels.length,
      tvgUrl: playlistAttributes.tvgUrl,
      catchup: playlistAttributes.catchup,
      catchupSource: playlistAttributes.catchupSource,
      userAgent: playlistAttributes.userAgent
    };
  }

  /**
   * 解析播放清單標頭屬性
   */
  private static parsePlaylistHeader(lines: string[]): {
    tvgUrl?: string;
    catchup?: string;
    catchupSource?: string;
    userAgent?: string;
  } {
    const attributes: any = {};

    // 查找 #EXTM3U 行
    const extm3uLine = lines.find(line => line.startsWith('#EXTM3U'));
    if (!extm3uLine) return attributes;

    // 解析標頭屬性
    const headerAttributes = [
      { key: 'x-tvg-url', prop: 'tvgUrl' },
      { key: 'catchup', prop: 'catchup' },
      { key: 'catchup-source', prop: 'catchupSource' },
      { key: 'user-agent', prop: 'userAgent' }
    ];

    headerAttributes.forEach(({ key, prop }) => {
      const regex = new RegExp(`${key}="([^"]*)"`, 'i');
      const match = extm3uLine.match(regex);
      if (match) {
        attributes[prop] = match[1];
      }
    });

    return attributes;
  }

  /**
   * 解析 #EXTINF 行
   */
  private static parseExtInf(line: string): Partial<M3UChannel> {
    const channel: Partial<M3UChannel> = {};

    // 提取持續時間（第一個數字）
    const durationMatch = line.match(/#EXTINF:\s*(-?\d+(?:\.\d+)?)/);
    if (durationMatch) {
      const duration = parseFloat(durationMatch[1]);
      if (duration > 0) {
        channel.duration = duration;
      }
    }

    // 提取頻道名稱（最後的部分）
    const nameMatch = line.match(/,(.+)$/);
    if (nameMatch) {
      channel.name = nameMatch[1].trim();
    }

    // 提取各種屬性
    const attributes = [
      { key: 'tvg-id', prop: 'tvgId' },
      { key: 'tvg-name', prop: 'tvgName' },
      { key: 'tvg-logo', prop: 'logo' },
      { key: 'group-title', prop: 'group' },
      { key: 'resolution', prop: 'resolution' },
      { key: 'language', prop: 'language' },
      { key: 'catchup', prop: 'catchup' },
      { key: 'catchup-source', prop: 'catchupSource' },
      { key: 'user-agent', prop: 'userAgent' },
      { key: 'referer', prop: 'referer' }
    ];

    attributes.forEach(({ key, prop }) => {
      const regex = new RegExp(`${key}="([^"]*)"`, 'i');
      const match = line.match(regex);
      if (match) {
        (channel as any)[prop] = match[1];
      }
    });

    return channel;
  }

  /**
   * 驗證 URL 是否有效
   */
  private static isValidUrl(url: string): boolean {
    try {
      // 清理 URL，移除可能的空白字符
      const cleanUrl = url.trim();

      // 基本格式檢查
      if (!cleanUrl || (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://'))) {
        return false;
      }

      // 嘗試創建 URL 對象來驗證格式
      new URL(cleanUrl);
      return true;
    } catch (error) {
      // 如果 URL 構造失敗，嘗試更寬鬆的驗證
      try {
        const cleanUrl = url.trim();
        // 簡單的正則表達式驗證
        const urlPattern = /^https?:\/\/[^\s/$.?#].[^\s]*$/i;
        return urlPattern.test(cleanUrl);
      } catch {
        return false;
      }
    }
  }

  /**
   * 安全的 Base64 編碼，處理 Unicode 字符
   */
  private static safeBase64Encode(str: string): string {
    try {
      // 使用 encodeURIComponent 和 btoa 來處理 Unicode 字符
      return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
        return String.fromCharCode(parseInt(p1, 16));
      }));
    } catch (error) {
      // 如果 btoa 失敗，使用簡單的哈希算法
      return this.simpleHash(str);
    }
  }

  /**
   * 簡單的哈希算法作為備用方案
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 轉換為 32 位整數
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 生成頻道 ID
   */
  private static generateChannelId(name: string, url: string): string {
    const combined = `${name}-${url}`;
    const encoded = this.safeBase64Encode(combined);
    return encoded.replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * 生成播放清單 ID
   */
  private static generatePlaylistId(url: string): string {
    const encoded = this.safeBase64Encode(url);
    return encoded.replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * 從 URL 提取播放清單名稱
   */
  private static extractPlaylistName(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      const filename = pathname.split('/').pop() || 'playlist';
      return filename.replace(/\.(m3u|m3u8)$/i, '');
    } catch {
      return 'Unknown Playlist';
    }
  }

  /**
   * 驗證頻道 URL 是否可播放
   */
  static async validateChannelUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * 從 GitHub 原始檔案 URL 解析 M3U
   * 自動轉換 GitHub 檔案 URL 為原始檔案 URL
   */
  static async parseFromGitHubUrl(url: string): Promise<ApiResponse<M3UPlaylist>> {
    try {
      // 轉換 GitHub URL 為原始檔案 URL
      const rawUrl = this.convertToGitHubRawUrl(url);
      return await this.parseFromUrl(rawUrl);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '解析 GitHub M3U 檔案時發生錯誤'
      };
    }
  }

  /**
   * 轉換 GitHub 檔案 URL 為原始檔案 URL
   */
  private static convertToGitHubRawUrl(url: string): string {
    // 如果已經是原始檔案 URL，直接返回
    if (url.includes('raw.githubusercontent.com')) {
      return url;
    }

    // 轉換 GitHub 檔案 URL
    // 從: https://github.com/user/repo/blob/branch/file.m3u
    // 到: https://raw.githubusercontent.com/user/repo/branch/file.m3u
    const githubPattern = /^https:\/\/github\.com\/([^\/]+)\/([^\/]+)\/blob\/(.+)$/;
    const match = url.match(githubPattern);

    if (match) {
      const [, user, repo, pathWithBranch] = match;
      return `https://raw.githubusercontent.com/${user}/${repo}/${pathWithBranch}`;
    }

    // 如果不是 GitHub URL，直接返回原 URL
    return url;
  }

  /**
   * 批量驗證頻道 URL（限制並發數量）
   */
  static async validateChannels(channels: M3UChannel[], maxConcurrent: number = 5): Promise<{
    valid: M3UChannel[];
    invalid: M3UChannel[];
    total: number;
  }> {
    const valid: M3UChannel[] = [];
    const invalid: M3UChannel[] = [];

    // 分批處理以避免過多並發請求
    for (let i = 0; i < channels.length; i += maxConcurrent) {
      const batch = channels.slice(i, i + maxConcurrent);
      const results = await Promise.allSettled(
        batch.map(async (channel) => {
          const isValid = await this.validateChannelUrl(channel.url);
          return { channel, isValid };
        })
      );

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          const { channel, isValid } = result.value;
          if (isValid) {
            valid.push(channel);
          } else {
            invalid.push(channel);
          }
        } else {
          // 如果驗證失敗，將頻道標記為無效
          const channel = batch[results.indexOf(result)];
          invalid.push(channel);
        }
      });
    }

    return {
      valid,
      invalid,
      total: channels.length
    };
  }

  /**
   * 多次重試驗證頻道 URL
   */
  static async validateChannelUrlWithRetry(url: string, retryCount: number = 3): Promise<boolean> {
    for (let i = 0; i < retryCount; i++) {
      const ok = await this.validateChannelUrl(url);
      if (ok) return true;
    }
    return false;
  }

  /**
   * 嘗試自動修復頻道 URL（補 http/https、去除空白、重試）
   * 所有驗證皆多次重試
   */
  static async autoFixChannelUrl(channel: M3UChannel): Promise<M3UChannel> {
    let fixedUrl = channel.url.trim();
    // 嘗試補上 http
    if (!/^https?:\/\//.test(fixedUrl)) {
      fixedUrl = 'http://' + fixedUrl;
    }
    // 多次重試 http
    let isValid = await this.validateChannelUrlWithRetry(fixedUrl, 3);
    if (!isValid) {
      // 嘗試 http/https 互換並重試
      if (fixedUrl.startsWith('http://')) {
        fixedUrl = fixedUrl.replace('http://', 'https://');
      } else if (fixedUrl.startsWith('https://')) {
        fixedUrl = fixedUrl.replace('https://', 'http://');
      }
      isValid = await this.validateChannelUrlWithRetry(fixedUrl, 3);
    }
    // 回傳修正後的頻道
    return {
      ...channel,
      url: fixedUrl
    };
  }

  /**
   * 批次自動修復異常頻道
   */
  static async autoFixChannels(channels: M3UChannel[], maxConcurrent: number = 5): Promise<{
    fixed: M3UChannel[];
    failed: M3UChannel[];
  }> {
    const fixed: M3UChannel[] = [];
    const failed: M3UChannel[] = [];
    for (let i = 0; i < channels.length; i += maxConcurrent) {
      const batch = channels.slice(i, i + maxConcurrent);
      const results = await Promise.all(
        batch.map(async (channel) => {
          const fixedChannel = await this.autoFixChannelUrl(channel);
          const isValid = await this.validateChannelUrl(fixedChannel.url);
          return { channel: fixedChannel, isValid };
        })
      );
      results.forEach(({ channel, isValid }) => {
        if (isValid) {
          fixed.push(channel);
        } else {
          failed.push(channel);
        }
      });
    }
    return { fixed, failed };
  }
}
