import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  // 根據部署平台設置 base
  base: process.env.NETLIFY ? '/' : (process.env.NODE_ENV === 'production' ? '/TVBox/' : '/'),
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    // 確保構建時不會因為警告而失敗
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          hls: ['hls.js'],
        }
      },
      // 忽略一些警告
      onwarn(warning, warn) {
        // 忽略 "use client" 警告
        if (warning.code === 'MODULE_LEVEL_DIRECTIVE') {
          return;
        }
        warn(warning);
      }
    },
    // 增加 chunk 大小限制
    chunkSizeWarningLimit: 1000
  },
  server: {
    port: 5173,
    host: true
  },
  // 預覽配置
  preview: {
    port: 4173,
    host: true
  }
})
